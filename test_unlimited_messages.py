#!/usr/bin/env python3
"""
Test script to verify unlimited message extraction works
"""

import asyncio
import logging
from message_user_extractor import MessageUserExtractor
from pyrogram import Client
from config import API_ID, API_HASH

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_unlimited_extraction(group_id: str):
    """Test unlimited message extraction"""
    print("\n" + "="*60)
    print("TESTING UNLIMITED MESSAGE EXTRACTION")
    print("="*60)
    
    extractor = MessageUserExtractor(None)  # No bot client needed
    
    async def progress_callback(message):
        print(f"[UNLIMITED] {message}")
    
    try:
        # Create user session
        print("[UNLIMITED] Creating user session...")
        success = await extractor.create_user_session("test_unlimited")
        if not success:
            print("[UNLIMITED] ❌ Failed to create user session")
            return 0, 0
        
        print("[UNLIMITED] ✅ User session created successfully")
        
        # Test unlimited extraction
        users, group_name, messages = await extractor.extract_users_and_messages_from_group(
            group_id, progress_callback
        )
        
        print(f"\n[UNLIMITED] ✅ RESULTS:")
        print(f"  Group: {group_name}")
        print(f"  Users found: {len(users):,}")
        print(f"  Messages found: {len(messages):,}")
        
        # Show some statistics
        if users:
            bots = sum(1 for u in users if u.get('is_bot', False))
            verified = sum(1 for u in users if u.get('is_verified', False))
            with_username = sum(1 for u in users if u.get('username'))
            
            print(f"\n[UNLIMITED] 📊 USER STATISTICS:")
            print(f"  Total Users: {len(users):,}")
            print(f"  Bots: {bots:,}")
            print(f"  Verified: {verified:,}")
            print(f"  With Username: {with_username:,}")
        
        if messages:
            text_msgs = sum(1 for m in messages if m.get('message_type') == 'text')
            media_msgs = sum(1 for m in messages if m.get('media_type'))
            
            print(f"\n[UNLIMITED] 📨 MESSAGE STATISTICS:")
            print(f"  Total Messages: {len(messages):,}")
            print(f"  Text Messages: {text_msgs:,}")
            print(f"  Media Messages: {media_msgs:,}")
        
        return len(users), len(messages)
        
    except Exception as e:
        print(f"[UNLIMITED] ❌ ERROR: {e}")
        return 0, 0
    finally:
        await extractor.stop_user_client()

async def test_simple_history_access(group_id: str):
    """Test simple direct access to chat history"""
    print("\n" + "="*60)
    print("TESTING DIRECT CHAT HISTORY ACCESS")
    print("="*60)
    
    try:
        # Create a simple user client
        user_client = Client("test_direct", api_id=API_ID, api_hash=API_HASH)
        await user_client.start()
        
        print("[DIRECT] ✅ User client started")
        
        # Test direct access
        message_count = 0
        user_count = 0
        unique_users = set()
        
        print(f"[DIRECT] Getting ALL messages from {group_id}...")
        
        async for message in user_client.get_chat_history(group_id):
            message_count += 1
            
            if message.from_user:
                if message.from_user.id not in unique_users:
                    unique_users.add(message.from_user.id)
                    user_count += 1
            
            # Progress every 1000 messages
            if message_count % 1000 == 0:
                print(f"[DIRECT] Processed {message_count:,} messages, found {user_count:,} unique users")
        
        print(f"\n[DIRECT] ✅ FINAL RESULTS:")
        print(f"  Total Messages: {message_count:,}")
        print(f"  Unique Users: {user_count:,}")
        
        await user_client.stop()
        return user_count, message_count
        
    except Exception as e:
        print(f"[DIRECT] ❌ ERROR: {e}")
        return 0, 0

async def main():
    """Main test function"""
    print("🧪 TESTING UNLIMITED MESSAGE EXTRACTION")
    print("This will verify that we can extract ALL messages and users from a group")
    
    # Get group ID from user
    group_id = input("\nEnter the group ID or username to test (e.g., @groupname or -1001234567890): ").strip()
    
    if not group_id:
        print("❌ No group ID provided")
        return
    
    print(f"\n🎯 Testing unlimited extraction from: {group_id}")
    
    # Test 1: Direct chat history access
    direct_users, direct_messages = await test_simple_history_access(group_id)
    
    # Test 2: Full extraction with user/message processing
    full_users, full_messages = await test_unlimited_extraction(group_id)
    
    # Compare results
    print("\n" + "="*60)
    print("📊 COMPARISON RESULTS")
    print("="*60)
    print(f"Direct Access:     {direct_messages:,} messages, {direct_users:,} users")
    print(f"Full Extraction:   {full_messages:,} messages, {full_users:,} users")
    print("="*60)
    
    if direct_messages > 0 and full_messages > 0:
        message_ratio = (full_messages / direct_messages) * 100
        user_ratio = (full_users / direct_users) * 100 if direct_users > 0 else 0
        
        print(f"Message Coverage:  {message_ratio:.1f}%")
        print(f"User Coverage:     {user_ratio:.1f}%")
        
        if message_ratio >= 95 and user_ratio >= 95:
            print("✅ EXCELLENT: Near-complete extraction achieved!")
        elif message_ratio >= 80 and user_ratio >= 80:
            print("✅ GOOD: High extraction coverage")
        else:
            print("⚠️  WARNING: Lower than expected coverage")
    
    if direct_messages > 1000:
        print(f"\n🎉 SUCCESS: Extracted {direct_messages:,} messages (way more than 500 limit!)")
        print("✅ The unlimited message extraction is working!")
    else:
        print(f"\n⚠️  Note: Only {direct_messages:,} messages found. This might be a small group.")

if __name__ == "__main__":
    asyncio.run(main())
