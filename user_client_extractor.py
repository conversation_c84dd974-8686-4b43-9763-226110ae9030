"""
Alternative user extractor using user client instead of bot client
This approach may have better access to group members
"""

from pyrogram import Client
from pyrogram.types import Chat<PERSON><PERSON>ber
from pyrogram.errors import <PERSON><PERSON>ait, <PERSON>t<PERSON><PERSON><PERSON><PERSON>equired, PeerIdInvalid, ChannelPrivate
from pyrogram import enums
from typing import List, Dict, Any
import asyncio
import logging
from config import API_ID, API_HASH

logger = logging.getLogger(__name__)


class UserClientExtractor:
    """
    Alternative extractor using user client for better member access
    """
    
    def __init__(self):
        self.user_client = None
    
    async def create_user_session(self, phone_number: str = None):
        """
        Create a user session for better access to group members
        Note: This requires a real phone number and verification
        """
        try:
            self.user_client = Client(
                "user_session",
                api_id=API_ID,
                api_hash=API_HASH,
                phone_number=phone_number
            )
            
            await self.user_client.start()
            me = await self.user_client.get_me()
            logger.info(f"User client started: {me.first_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create user session: {e}")
            return False
    
    async def extract_users_with_user_client(self, group_identifier: str, progress_callback=None) -> tuple[List[Dict[str, Any]], str]:
        """
        Extract users using user client (has better access than bot)
        """
        if not self.user_client:
            raise ValueError("User client not initialized. Call create_user_session first.")
        
        try:
            # Get chat information
            chat = await self.user_client.get_chat(group_identifier)
            group_name = chat.title or str(group_identifier)
            
            if progress_callback:
                await progress_callback(f"📊 Found group: {group_name}")
                await progress_callback(f"👥 Total members: {chat.members_count}")
                await progress_callback(f"🔍 Using USER CLIENT for better access")
            
            users_data = []
            all_user_ids = set()
            
            # Method 1: Get all members with proper pagination (user clients have better access)
            if progress_callback:
                await progress_callback("🔄 Extracting ALL members using USER CLIENT (comprehensive)...")

            try:
                offset = 0
                limit = 200  # Process in chunks
                max_members = 100000  # Higher limit for user clients
                total_extracted = 0

                while total_extracted < max_members:
                    try:
                        # Get members in chunks with proper pagination
                        chunk_members = []

                        async for member in self.user_client.get_chat_members(
                            group_identifier,
                            limit=limit,
                            offset=offset
                        ):
                            chunk_members.append(member)

                        # If no members returned, we've reached the end
                        if not chunk_members:
                            if progress_callback:
                                await progress_callback("✅ Reached end of member list")
                            break

                        # Process the chunk
                        chunk_processed = 0
                        for member in chunk_members:
                            try:
                                if member.user.id in all_user_ids:
                                    continue

                                user_data = self._extract_user_data(member)
                                users_data.append(user_data)
                                all_user_ids.add(member.user.id)
                                chunk_processed += 1
                                total_extracted += 1

                            except Exception as e:
                                logger.warning(f"Error extracting user: {e}")
                                continue

                        # Update progress
                        if progress_callback and total_extracted % 200 == 0:
                            await progress_callback(f"📥 Extracted {total_extracted} users...")

                        # Update offset for next chunk
                        offset += len(chunk_members)

                        # If we got fewer members than the limit, we're likely at the end
                        if len(chunk_members) < limit:
                            if progress_callback:
                                await progress_callback(f"📉 Received {len(chunk_members)} members (less than limit), extraction complete")
                            break

                        # Small delay to avoid rate limiting
                        await asyncio.sleep(0.3)

                    except FloodWait as e:
                        if progress_callback:
                            await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                        await asyncio.sleep(e.value)
                        continue
                    except Exception as e:
                        logger.warning(f"Error in chunk processing: {e}")
                        break

            except FloodWait as e:
                if progress_callback:
                    await progress_callback(f"⏳ Rate limited. Waiting {e.value} seconds...")
                await asyncio.sleep(e.value)
            
            # Method 2: Try different filters for more coverage with pagination
            filters = [
                enums.ChatMembersFilter.RECENT,
                enums.ChatMembersFilter.SEARCH,
                enums.ChatMembersFilter.ADMINISTRATORS,
                enums.ChatMembersFilter.BOTS,
            ]

            for filter_type in filters:
                try:
                    if progress_callback:
                        await progress_callback(f"🔍 Trying {filter_type.name} filter with pagination...")

                    filter_offset = 0
                    filter_limit = 200
                    filter_count = 0

                    while filter_count < 10000:  # Limit per filter
                        try:
                            chunk_members = []
                            async for member in self.user_client.get_chat_members(
                                group_identifier,
                                filter=filter_type,
                                limit=filter_limit,
                                offset=filter_offset
                            ):
                                chunk_members.append(member)

                            if not chunk_members:
                                break

                            chunk_new = 0
                            for member in chunk_members:
                                if member.user.id not in all_user_ids:
                                    user_data = self._extract_user_data(member)
                                    users_data.append(user_data)
                                    all_user_ids.add(member.user.id)
                                    chunk_new += 1
                                    filter_count += 1

                            filter_offset += len(chunk_members)

                            if len(chunk_members) < filter_limit:
                                break

                            await asyncio.sleep(0.2)

                        except FloodWait as e:
                            await asyncio.sleep(e.value)
                            continue
                        except Exception as e:
                            logger.warning(f"Error in filter chunk: {e}")
                            break

                    if progress_callback and filter_count > 0:
                        await progress_callback(f"✅ {filter_type.name} found {filter_count} additional users")

                except Exception as e:
                    logger.warning(f"Error with filter {filter_type}: {e}")
                    continue
            
            # Method 3: Extract from ALL message history (unlimited)
            if progress_callback:
                await progress_callback("📜 Extracting from ALL message history (unlimited)...")

            try:
                message_count = 0
                history_users = 0

                # Get ALL messages without limit
                async for message in self.user_client.get_chat_history(group_identifier):
                    message_count += 1

                    if message.from_user and message.from_user.id not in all_user_ids:
                        try:
                            # Create fake member for consistency
                            fake_member = type('ChatMember', (), {
                                'user': message.from_user,
                                'status': enums.ChatMemberStatus.MEMBER,
                                'joined_date': None,
                            })()

                            user_data = self._extract_user_data(fake_member)
                            users_data.append(user_data)
                            all_user_ids.add(message.from_user.id)
                            history_users += 1

                        except Exception as e:
                            logger.warning(f"Error extracting from message: {e}")
                            continue

                    # Extract from forwarded messages too
                    if (hasattr(message, 'forward_from') and message.forward_from and
                        message.forward_from.id not in all_user_ids):
                        try:
                            fake_member = type('ChatMember', (), {
                                'user': message.forward_from,
                                'status': enums.ChatMemberStatus.MEMBER,
                                'joined_date': None,
                            })()

                            user_data = self._extract_user_data(fake_member)
                            users_data.append(user_data)
                            all_user_ids.add(message.forward_from.id)
                            history_users += 1

                        except Exception as e:
                            logger.warning(f"Error extracting from forward: {e}")
                            continue

                    if progress_callback and message_count % 2000 == 0:
                        await progress_callback(f"📜 Processed {message_count:,} messages, found {history_users:,} users...")

                if progress_callback:
                    await progress_callback(
                        f"📜 Message history complete: {message_count:,} messages processed, "
                        f"{history_users:,} users found"
                    )

            except Exception as e:
                logger.warning(f"Error extracting from history: {e}")
            
            if progress_callback:
                await progress_callback(f"✅ Extraction complete! Total users: {len(users_data)}")
            
            return users_data, group_name
            
        except Exception as e:
            logger.error(f"Error in user client extraction: {e}")
            raise
    
    def _extract_user_data(self, member: ChatMember) -> Dict[str, Any]:
        """Extract user data from ChatMember object"""
        user = member.user
        
        user_data = {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': f"{user.first_name or ''} {user.last_name or ''}".strip(),
            'is_bot': user.is_bot,
            'is_verified': getattr(user, 'is_verified', False),
            'is_premium': getattr(user, 'is_premium', False),
            'is_scam': getattr(user, 'is_scam', False),
            'is_fake': getattr(user, 'is_fake', False),
            'is_restricted': getattr(user, 'is_restricted', False),
            'language_code': getattr(user, 'language_code', None),
            'dc_id': getattr(user, 'dc_id', None),
            'phone_number': getattr(user, 'phone_number', None),
            'status': member.status.name if member.status else 'UNKNOWN',
            'joined_date': member.joined_date.isoformat() if getattr(member, 'joined_date', None) else None,
            'is_member': member.status.name in ['MEMBER', 'ADMINISTRATOR', 'OWNER'] if member.status else False,
        }
        
        # Last online information
        if hasattr(user, 'last_online_date') and user.last_online_date:
            user_data['last_online_date'] = user.last_online_date.isoformat()
        else:
            user_data['last_online_date'] = None
        
        # Admin information
        if member.status and member.status.name in ['ADMINISTRATOR', 'OWNER']:
            user_data.update({
                'is_admin': True,
                'admin_title': getattr(member, 'custom_title', None),
                'can_be_edited': getattr(member, 'can_be_edited', False),
                'can_manage_chat': getattr(member, 'can_manage_chat', False),
                'can_delete_messages': getattr(member, 'can_delete_messages', False),
                'can_manage_video_chats': getattr(member, 'can_manage_video_chats', False),
                'can_restrict_members': getattr(member, 'can_restrict_members', False),
                'can_promote_members': getattr(member, 'can_promote_members', False),
                'can_change_info': getattr(member, 'can_change_info', False),
                'can_invite_users': getattr(member, 'can_invite_users', False),
                'can_pin_messages': getattr(member, 'can_pin_messages', False),
            })
        else:
            user_data['is_admin'] = False
        
        return user_data
    
    async def stop(self):
        """Stop the user client"""
        if self.user_client:
            await self.user_client.stop()
            self.user_client = None
